
#include "UniTextAPIExport.h"
#include <unordered_map>
#include "Utility/TextUtility.h"
#include "UniTextGlobal.h"
#include "Common/UniMacro.h"

NAMESPACE_USE

enum ErrorDefine : unsigned char
{
    None = 0,
    FontPathInvalid,
    TextureIsEmpty,

};

/// global path-font map
static std::unordered_map<const char*, UniFont*> gCachedFonts;

void RegisterCreateTextureCallback(CSharp_NewTextureFromUnity callback)
{
    //gNewTextureCallback = callback;
}

UniFont* CreateNativeFont(void* CSharp_UniFont, const char* fontPath)
{
    if (fontPath == nullptr) return nullptr;
    //UniFont* font = UniFont::CreateFromFile(fontPath);
    //font->SetCSharpHandle(CSharp_UniFont);
    return nullptr;
}

void DestroyNativeFont(void* Cpp_UniFont)
{
    if (Cpp_UniFont == nullptr) return;

    UniFont* font = (UniFont*)Cpp_UniFont;
    DELETE_PTR(font);
}

void RegisterCSharpCallback(CSharp_NewTextureFromUnity function)
{
    //fCSharp_NewTextureFromUnity = function;
}

// void SetLogHandler(LogFunction _logHandler)
// {
//     UniText::SetLogHandler(_logHandler);
// }

#pragma region UniTextGenerator
UniTextGenerator* CreateUniText()
{
    return new UniTextGenerator();
}

void DestroyUniText(UniTextGenerator* uniText)
{
    DELETE_PTR(uniText);
}

/*
void SetFontPath(UniTextGenerator* uniText, const char* fontPath)
{
    if (uniText != nullptr)
    {
        uniText->SetFont(fontPath);
    }
}
*/

void SetFont(UniTextGenerator* uniText, UniFont* font)
{
    if (uniText != nullptr)
    {
        uniText->SetFont(font);
    }
}

void SetFontSize(UniTextGenerator* uniText, int fontSize)
{
    if (uniText != nullptr)
    {
        uniText->SetFontSize(fontSize);
    }
}

void SetText(UniTextGenerator* uniText, const char* text)
{
    if (uniText != nullptr)
    {
        uniText->SetText(text);
    }
}

void SetExtents(UniTextGenerator* uniText, float width, float height)
{
    if (uniText != nullptr)
    {
        uniText->SetExtents(width, height);
    }
}

void SetPivot(UniTextGenerator* uniText, float pivot_x, float pivot_y) 
{
    if (uniText != nullptr) 
    {
        uniText->SetPivot(Vector2f{pivot_x, pivot_y});
    }
}

void SetHorizontalAlignment(UniTextGenerator* uniText, int alignment)
{
    if (uniText != nullptr)
    {
        uniText->SetHorizontalAlignment(static_cast<TextAlignment>(alignment));
    }
}

void SetVerticalAlignment(UniTextGenerator* uniText, int alignment)
{
    if (uniText != nullptr)
    {
        uniText->SetVerticalAlignment(static_cast<TextAlignment>(alignment));
    }
}

void SetHorizontalOverflow(UniTextGenerator* uniText, int overflow)
{
    if (uniText != nullptr)
    {
        uniText->SetHorizontalOverflow(static_cast<TextOverflow>(overflow));
    }
}

void SetVerticalOverflow(UniTextGenerator* uniText, int overflow)
{
    if (uniText != nullptr)
    {
        uniText->SetVerticalOverflow(static_cast<TextOverflow>(overflow));
    }
}

void SetCharacterSpacing(UniTextGenerator* uniText, float spacing)
{
    if (uniText != nullptr)
    {
        uniText->SetCharacterSpacing(spacing);
    }
}

void SetLineSpacing(UniTextGenerator* uniText, float spacing)
{
    if (uniText != nullptr)
    {
        uniText->SetLineSpacing(spacing);
    }
}

void SetActive(UniTextGenerator* uniText, bool active)
{
    if (uniText != nullptr)
    {
        uniText->SetActive(active);
    }
}

int GetFontSize(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return uniText->GetFontSize();
    }

    return 0;
}

int GetHorizontalAlignment(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return static_cast<int>(uniText->GetHorizontalAlignment());
    }

    return 0;
}

int GetVerticalAlignment(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return static_cast<int>(uniText->GetVerticalAlignment());
    }

    return 0;
}

int GetHorizontalOverflow(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return static_cast<int>(uniText->GetHorizontalOverflow());
    }

    return 0;
}

int GetVerticalOverflow(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return static_cast<int>(uniText->GetVerticalOverflow());
    }

    return 0;
}

float GetCharacterSpacing(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return uniText->GetCharacterSpacing();
    }

    return 0.0f;
}

float GetLineSpacing(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return uniText->GetLineSpacing();
    }

    return 0.0f;
}

int GetVertexCount(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return static_cast<int>(uniText->GetMeshData().vertices.size());
    }

    return 0;
}

void FillVertices(UniTextGenerator* uniText, UniTextGenerator::Vertex* outVertexArray, int start, int count)
{
    if (uniText != nullptr)
    {
        auto& meshData = uniText->GetMeshData();
        auto maxSize = std::min<int>(start + count, meshData.vertices.size());
        if (start < maxSize)
        {
            for (int i_s = start, i_d = 0; i_s < maxSize; i_s++,i_d++)
            {
                outVertexArray[i_d].color = meshData.colors[i_s];
                outVertexArray[i_d].position = meshData.vertices[i_s];
                outVertexArray[i_d].uv = meshData.uvs[i_s];
            }
        }
    }
}

void FillVerticesPerChannel(UniTextGenerator* uniText, Vector2f* outPositionArray, Vector4f* outUvArray, Color* outColorArray, int start, int count)
{
    if (uniText != nullptr)
    {
        auto& meshData = uniText->GetMeshData();
        auto sizeToCopy = std::min<int>(count, meshData.vertices.size());
        if (start + sizeToCopy <= meshData.vertices.size())
        {
            std::memcpy(outPositionArray, &meshData.vertices[start], sizeToCopy * sizeof(Vector2f));
            std::memcpy(outUvArray, &meshData.uvs[start], sizeToCopy * sizeof(Vector4f));
            std::memcpy(outColorArray, &meshData.colors[start], sizeToCopy * sizeof(Color));
            /*
            for (int i_s = start, i_d = 0; i_s < maxSize; i_s++, i_d++)
            {
                outVertexArray[i_d].color = meshData.colors[i_s];
                outVertexArray[i_d].position = meshData.vertices[i_s];
                outVertexArray[i_d].uv = meshData.uvs[i_s];
            }
            */
        }
    }
}

void Rebuild(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        uniText->Rebuild();
    }
}

int GetTextureCount()
{
    return UniTextGlobal::GetAtlasCache()->GetTextureCount();
}

void* GetTextureHandle(int textureIndex)
{
    auto* texture = UniTextGlobal::GetAtlasCache()->GetTexture(textureIndex);
    if (texture != nullptr)
    {
        return texture->GetTextureHandle();
    }

    return nullptr;
}

void ApplyFontAtlases()
{
    //UniTextGlobal::GetAtlasCache()->ApplyDirtyTextures();
}

// New UniTextGenerator APIs
void SetColor(UniTextGenerator* uniText, float r, float g, float b, float a)
{
    if (uniText != nullptr)
    {
        uniText->SetColor(Color(r, g, b, a));
    }
}

void SetStrokeSize(UniTextGenerator* uniText, float strokeSize)
{
    if (uniText != nullptr)
    {
        uniText->SetStrokeSize(strokeSize);
    }
}

void SetFontStyle(UniTextGenerator* uniText, int fontStyle)
{
    if (uniText != nullptr)
    {
        uniText->SetFontStyle(static_cast<FontStyle>(fontStyle));
    }
}

void SetAutoSizeRange(UniTextGenerator* uniText, int minFontSize, int maxFontSize)
{
    if (uniText != nullptr)
    {
        uniText->SetAutoSizeRange(minFontSize, maxFontSize);
    }
}

void EnableRichText(UniTextGenerator* uniText, bool enabled)
{
    if (uniText != nullptr)
    {
        uniText->EnableRichText(enabled);
    }
}

void EnableKerning(UniTextGenerator* uniText, bool enabled)
{
    if (uniText != nullptr)
    {
        uniText->EnableKerning(enabled);
    }
}

void EnableAutoSize(UniTextGenerator* uniText, bool enabled)
{
    if (uniText != nullptr)
    {
        uniText->EnableAutoSize(enabled);
    }
}

void EnableRTL(UniTextGenerator* uniText, bool enabled)
{
    if (uniText != nullptr)
    {
        uniText->EnableRTL(enabled);
    }
}

void AppendText(UniTextGenerator* uniText, const char* text)
{
    if (uniText != nullptr)
    {
        uniText->AppendText(text);
    }
}

void GetColor(UniTextGenerator* uniText, float* r, float* g, float* b, float* a)
{
    if (uniText != nullptr && r != nullptr && g != nullptr && b != nullptr && a != nullptr)
    {
        Color color = uniText->GetColor();
        *r = color.r;
        *g = color.g;
        *b = color.b;
        *a = color.a;
    }
}

float GetStrokeSize(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return uniText->GetStrokeSize();
    }
    return 0.0f;
}

void GetPivot(UniTextGenerator* uniText, float* pivot_x, float* pivot_y)
{
    if (uniText != nullptr && pivot_x != nullptr && pivot_y != nullptr)
    {
        Vector2f pivot = uniText->GetPivot();
        *pivot_x = pivot.x;
        *pivot_y = pivot.y;
    }
}

int GetFontStyle(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return static_cast<int>(uniText->GetFontStyle());
    }
    return 0;
}

void GetAutoSizeRange(UniTextGenerator* uniText, int* minFontSize, int* maxFontSize)
{
    if (uniText != nullptr && minFontSize != nullptr && maxFontSize != nullptr)
    {
        auto range = uniText->GetAutoSizeRange();
        *minFontSize = std::get<0>(range);
        *maxFontSize = std::get<1>(range);
    }
}

bool HasRichText(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return uniText->HasRichText();
    }
    return false;
}

bool HasKerning(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return uniText->HasKerning();
    }
    return false;
}

bool HasAutoSize(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return uniText->HasAutoSize();
    }
    return false;
}

bool HasRTL(UniTextGenerator* uniText)
{
    if (uniText != nullptr)
    {
        return uniText->HasRTL();
    }
    return false;
}

#pragma endregion


#pragma region UniFont
UniFont* GetFont(const char* fontPath)
{
    return UniTextGlobal::GetFontCache()->GetFont<false>(fontPath);
}

UniFont* LoadFontFromPath(const char* fontPath)
{
    LOG_INFO("Loading font from path: %s", fontPath ? fontPath : "(null)");

    if (fontPath == nullptr)
    {
        LOG_ERROR("Font path is null");
        return nullptr;
    }

    auto* font = UniTextGlobal::GetFontCache()->GetFont<true>(fontPath);
    if (font != nullptr)
    {
        LOG_DEBUG("Font loaded successfully: %s", fontPath);
    }
    else
    {
        LOG_ERROR("Failed to load font: %s", fontPath);
    }

    return font;
}

UniFont* LoadFontFromMemory(const char* fontName, const unsigned char* fileBuffer, unsigned long bufferSize)
{
    LOG_INFO("Loading font from memory: %s (size: %lu bytes)", fontName ? fontName : "(null)", bufferSize);

    if (fontName == nullptr)
    {
        LOG_ERROR("Font name is null");
        return nullptr;
    }

    if (fileBuffer == nullptr || bufferSize == 0)
    {
        LOG_ERROR("Invalid font buffer or size for font: %s", fontName);
        return nullptr;
    }

    auto* font = UniTextGlobal::GetFontCache()->LoadFontFromMemory(fontName, fileBuffer, bufferSize);
    if (font != nullptr)
    {
        LOG_DEBUG("Font loaded successfully from memory: %s", fontName);
    }
    else
    {
        LOG_ERROR("Failed to load font from memory: %s", fontName);
    }

    return font;
}

void SetPackingMothod(UniFont* uniFont, PackingMethod packingMethod)
{
    if (uniFont != nullptr)
    {
        uniFont->SetPackingMethod(packingMethod);
    }
}

void SetPadding(UniFont* uniFont, uint8 padding)
{
    if (uniFont != nullptr)
    {
        uniFont->SetPadding(padding);
    }
}

void SetRenderMode(UniFont* uniFont, RenderMode renderMode)
{
    if (uniFont != nullptr)
    {
        uniFont->SetRenderMode(renderMode);
    }
}

void SetFontSizeRange(UniFont* font, int minSize, int maxSize)
{
    if (font != nullptr)
    {
        font->SetFontSizeRange(minSize, maxSize);
    }
}

void TryPackGlyphs(UniFont* font, const char* text, int fontSize)
{
    if (font != nullptr)
    {
        UTF16String utfStr(text);
        int roundedSize = RoundFontSize(fontSize, font->GetMinFontSize(), font->GetMaxFontSize());
        for (size_t i = 0; i < utfStr.length; i++)
        {
            const auto unicode = utfStr.data[i];
            GlyphInfo* info;
            GlyphLoadParam param = {unicode, roundedSize, RenderMode::NotSpecified};
            font->LoadGlyph(param, &info);
        }
    }
}

void UnloadUnusedGlyphs(UniFont* font)
{
    if (font != nullptr)
    {
        font->UnloadUnusedGlyphs();
    }
}

void ClearGlyphs(UniFont* font)
{
    if (font != nullptr)
    {
        font->ClearGlyphs();
    }
}

// New UniFont APIs
void AddFallbackFont(UniFont* font, const char* fontName)
{
    if (font != nullptr && fontName != nullptr)
    {
        font->AddFallbackFont(fontName);
    }
}

const char* GetFamilyName(UniFont* font)
{
    if (font != nullptr)
    {
        // Return a pointer to the family name string
        // Note: This assumes the string remains valid for the lifetime of the font
        static thread_local std::string familyName;
        familyName = font->GetFamilyName();
        return familyName.c_str();
    }
    return nullptr;
}

int GetStyle(UniFont* font)
{
    if (font != nullptr)
    {
        return static_cast<int>(font->GetFontStyle());
    }
    return 0;
}

int GetFontGUID(UniFont* font)
{
    if (font != nullptr)
    {
        return font->GetFontGUID();
    }
    return 0;
}

void GetFontSizeRange(UniFont* font, int* minSize, int* maxSize)
{
    if (font != nullptr && minSize != nullptr && maxSize != nullptr)
    {
        *minSize = font->GetMinFontSize();
        *maxSize = font->GetMaxFontSize();
    }
}

#pragma endregion


#pragma region UniFontAtlas
void ClearFontAtlases()
{

}
#pragma endregion


#pragma region FontSettings
bool InitializeLibrary()
{
    LOG_INFO("Initializing UniText library via API...");
    bool result = UniTextGlobal::Initialize();
    if (result)
    {
        LOG_INFO("UniText library API initialization successful");
    }
    else
    {
        LOG_ERROR("UniText library API initialization failed");
    }
    return result;
}

void DestroyLibrary()
{
    LOG_INFO("Destroying UniText library...");
    UniTextGlobal::Destroy();
    LOG_INFO("UniText library destroyed");
}

int GetMaxTextureCount()
{
    return UniTextGlobal::GetSettings().GetMaxTextureCount();
}

void SetMaxTextureCount(int count)
{
    UniTextGlobal::GetMutableSettings().SetMaxTextureCount(count);
}

int GetMinTextureEntrySize()
{
    return UniTextGlobal::GetSettings().GetMinTextureEntrySize();
}

void SetMinTextureEntrySize(int size)
{
    UniTextGlobal::GetMutableSettings().SetMinTextureEntrySize(size);
}

int GetMaxTextureSize()
{
    return UniTextGlobal::GetSettings().GetMaxTextureSize();
}

void SetMaxTextureSize(int size)
{
    UniTextGlobal::GetMutableSettings().SetMaxTextureSize(size);
}

int GetFontSizeStep()
{
    return UniTextGlobal::GetSettings().GetFontSizeStep();
}

void SetFontSizeStep(int step)
{
    UniTextGlobal::GetMutableSettings().SetFontSizeStep(step);
}

void ResetToDefaults()
{
    UniTextGlobal::GetMutableSettings().Reset();
}

void UpdateAllFontsIfSettingsChanged()
{
    UniFont::UpdateAllFontsIfSettingsChanged();
}
#pragma endregion