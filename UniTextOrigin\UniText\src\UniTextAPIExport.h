#ifndef __UniTextAPIExport_h__
#define __UniTextAPIExport_h__

#include "Common/UniPlatform.h"
#include "Font/UniFont.h"
#include "UniTextGenerator.h"

NAMESPACE_USE

#ifdef __cplusplus
extern "C"
#endif
{
    /// Export core functions to Unity Scripts
    /// On the other hand, this project should work with out Unity or any other engine

    /// UniFont.cs/.h
    ///////////////////////////////////////////////////////////////
    // Cpp callback to C#
    UNITEXT_API void RegisterCreateTextureCallback(CSharp_NewTextureFromUnity callback);
    ///////////////////////////////////////////////////////////////

    ///////////////////////////////////////////////////////////////
    // C# calling Cpp
    UNITEXT_API UniFont* CreateNativeFont(void* CSharp_UniFont, const char* fontPath);
    UNITEXT_API void DestroyNativeFont(void* Cpp_UniFont);
    // Logging is moved to UnityPluginEntry.cpp
    // UNITEXT_API void SetLogHandler(LogFunction _logHandler);
    ///////////////////////////////////////////////////////////////

#pragma region UniTextGenerator
    UNITEXT_API UniTextGenerator* CreateUniText();
    UNITEXT_API void DestroyUniText(UniTextGenerator* uniText);

    /// <summary>
    /// Set Properties
    /// </summary>
    //UNITEXT_API void SetFontPath(UniTextGenerator* uniText, const char* fontPath);
    UNITEXT_API void SetFont(UniTextGenerator* uniText, UniFont* font);
    UNITEXT_API void SetFontSize(UniTextGenerator* uniText, int fontSize);
    UNITEXT_API void SetText(UniTextGenerator* uniText, const char* text);
    UNITEXT_API void AppendText(UniTextGenerator* uniText, const char* text);
    UNITEXT_API void SetExtents(UniTextGenerator* uniText, float width, float height);
    UNITEXT_API void SetPivot(UniTextGenerator* uniText, float pivot_x, float pivot_y);
    UNITEXT_API void SetHorizontalAlignment(UniTextGenerator* uniText, int alignment);
    UNITEXT_API void SetVerticalAlignment(UniTextGenerator* uniText, int alignment);
    UNITEXT_API void SetHorizontalOverflow(UniTextGenerator* uniText, int overflow);
    UNITEXT_API void SetVerticalOverflow(UniTextGenerator* uniText, int overflow);
    UNITEXT_API void SetCharacterSpacing(UniTextGenerator* uniText, float spacing);
    UNITEXT_API void SetLineSpacing(UniTextGenerator* uniText, float spacing);
    UNITEXT_API void SetActive(UniTextGenerator* uniText, bool active);
    UNITEXT_API void SetColor(UniTextGenerator* uniText, float r, float g, float b, float a);
    UNITEXT_API void SetStrokeSize(UniTextGenerator* uniText, float strokeSize);
    UNITEXT_API void SetFontStyle(UniTextGenerator* uniText, int fontStyle);
    UNITEXT_API void SetAutoSizeRange(UniTextGenerator* uniText, int minFontSize, int maxFontSize);
    UNITEXT_API void EnableRichText(UniTextGenerator* uniText, bool enabled);
    UNITEXT_API void EnableKerning(UniTextGenerator* uniText, bool enabled);
    UNITEXT_API void EnableAutoSize(UniTextGenerator* uniText, bool enabled);
    UNITEXT_API void EnableRTL(UniTextGenerator* uniText, bool enabled);

    /// <summary>
    /// Get Properties
    /// </summary>
    UNITEXT_API int GetFontSize(UniTextGenerator* uniText);
    UNITEXT_API int GetHorizontalAlignment(UniTextGenerator* uniText);
    UNITEXT_API int GetVerticalAlignment(UniTextGenerator* uniText);
    UNITEXT_API int GetHorizontalOverflow(UniTextGenerator* uniText);
    UNITEXT_API int GetVerticalOverflow(UniTextGenerator* uniText);
    UNITEXT_API float GetCharacterSpacing(UniTextGenerator* uniText);
    UNITEXT_API float GetLineSpacing(UniTextGenerator* uniText);
    UNITEXT_API void GetColor(UniTextGenerator* uniText, float* r, float* g, float* b, float* a);
    UNITEXT_API float GetStrokeSize(UniTextGenerator* uniText);
    UNITEXT_API void GetPivot(UniTextGenerator* uniText, float* pivot_x, float* pivot_y);
    UNITEXT_API int GetFontStyle(UniTextGenerator* uniText);
    UNITEXT_API void GetAutoSizeRange(UniTextGenerator* uniText, int* minFontSize, int* maxFontSize);
    UNITEXT_API bool HasRichText(UniTextGenerator* uniText);
    UNITEXT_API bool HasKerning(UniTextGenerator* uniText);
    UNITEXT_API bool HasAutoSize(UniTextGenerator* uniText);
    UNITEXT_API bool HasRTL(UniTextGenerator* uniText);

    /// <summary>
    /// Mesh Generation
    /// </summary>
    UNITEXT_API int GetVertexCount(UniTextGenerator* uniText);
    UNITEXT_API void FillVertices(UniTextGenerator* uniText, UniTextGenerator::Vertex* outVertexArray, int start, int count);
    UNITEXT_API void FillVerticesPerChannel(UniTextGenerator* uniText, Vector2f* outPositionArray, Vector4f* outUvArray, Color* outColorArray, int start, int count);
    UNITEXT_API void Rebuild(UniTextGenerator* uniText);

    /// <summary>
    /// Texture Aquire
    /// </summary>
    UNITEXT_API int GetTextureCount();
    UNITEXT_API void* GetTextureHandle(int textureIndex);
    //UNITEXT_API void* GetTextureHandle(UniTextGenerator* uniText, int pageIndex);
    //UNITEXT_API void ApplyFontAtlases();
#pragma endregion

#pragma region UniFont
    UNITEXT_API UniFont* GetFont(const char* fontPath);
    UNITEXT_API UniFont* LoadFontFromPath(const char* fontPath);
    UNITEXT_API UniFont* LoadFontFromMemory(const char* fontPath, const unsigned char* fileBuffer, unsigned long bufferSize);

    UNITEXT_API void SetPackingMothod(UniFont* uniFont, PackingMethod packingMethod);
    UNITEXT_API void SetPadding(UniFont* uniFont, uint8 padding);
    UNITEXT_API void SetRenderMode(UniFont* uniFont, RenderMode renderMode);
    UNITEXT_API void SetFontSizeRange(UniFont* uniFont, int minSize, int maxSize);
    UNITEXT_API void AddFallbackFont(UniFont* uniFont, const char* fontPath);

    /// <summary>
    /// Get Font Properties
    /// </summary>
    UNITEXT_API const char* GetFamilyName(UniFont* uniFont);
    UNITEXT_API int GetStyle(UniFont* uniFont);
    UNITEXT_API int GetFontGUID(UniFont* uniFont);
    UNITEXT_API void GetFontSizeRange(UniFont* uniFont, int* minSize, int* maxSize);

    /// <summary>
    /// Benchmarking with UGUI & TextMeshPro
    /// </summary>
    UNITEXT_API void TryPackGlyphs(UniFont* uniFont, const char* text, int fontSize);
    UNITEXT_API void UnloadUnusedGlyphs(UniFont* uniFont);
    UNITEXT_API void ClearGlyphs(UniFont* uniFont);
#pragma endregion

#pragma region UniFontAtlas
    UNITEXT_API void ClearFontAtlases();
#pragma endregion

#pragma region FontSettings    
    /**
     * @brief Initialize & Destroy the UniText Library
     * For Unity integration, those are handled in UnityPluginEntry.cpp
     */
    UNITEXT_API bool InitializeLibrary();
    UNITEXT_API void DestroyLibrary();

    UNITEXT_API int GetMaxTextureCount();
    UNITEXT_API void SetMaxTextureCount(int count);
    UNITEXT_API int GetMinTextureEntrySize();
    UNITEXT_API void SetMinTextureEntrySize(int size);
    UNITEXT_API int GetMaxTextureSize();
    UNITEXT_API void SetMaxTextureSize(int size);
    UNITEXT_API int GetFontSizeStep();
    UNITEXT_API void SetFontSizeStep(int step);
    UNITEXT_API void ResetToDefaults();
    UNITEXT_API void UpdateAllFontsIfSettingsChanged();
#pragma endregion

}

#endif // __UniTextAPIExport_h__