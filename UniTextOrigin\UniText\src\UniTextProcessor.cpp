#include "UniTextProcessor.h"
#include "Font/UniFont.h"
#include "UniTextGenerator.h"
#include "Utility/TextUtility.h"

#if USE_ROUND_POSITION
#include <cmath>
#define ROUND(x) std::round(x)
#else
#define ROUND(x) x
#endif

NAMESPACE_BEGIN

//////////////////////////////////////////////////////////////////////////
// UnifiedTextProcessor Implementation
//////////////////////////////////////////////////////////////////////////

UnifiedTextProcessor::UnifiedTextProcessor()
    : m_simpleProcessor(std::make_unique<SimpleTextProcessor>())
#if USE_HARFBUZZ
    , m_harfBuzzProcessor(std::make_unique<HarfBuzzTextProcessor>())
#endif
{
}

bool UnifiedTextProcessor::ProcessText(const TextProcessingParams& params)
{
    if (!params.generator) {
        return false;
    }

    auto* generator = params.generator;

    // Determine processing direction
    ProcessingDirection direction = params.direction;
    if (direction == ProcessingDirection::Auto) {
        direction = AutoDetectDirection(generator->GetUnicodes());
    }

    // Analyze text complexity
    bool hasRTL = (direction == ProcessingDirection::RightToLeft);
    TextComplexity complexity = AnalyzeTextComplexity(generator->GetUnicodes(), hasRTL);

    // Select appropriate processor
    ITextProcessor* processor = SelectProcessor(complexity);
    if (!processor) {
        return false;
    }

    // Create modified params with resolved direction
    TextProcessingParams resolvedParams = params;
    resolvedParams.direction = direction;

    // Process the text
    return processor->ProcessText(resolvedParams);
}

TextComplexity UnifiedTextProcessor::AnalyzeTextComplexity(const std::pmr::vector<uint16>& unicodes, bool hasRTL)
{
    // Check for complex scripts that require HarfBuzz
    for (uint16 unicode : unicodes) {
        if (IsComplexScript(unicode) || RequiresShaping(unicode)) {
            return TextComplexity::Complex;
        }
    }

    // For now, treat RTL as simple if no complex scripts are detected
    // This allows basic RTL (like Hebrew/Arabic without complex shaping) to use the fast path
    // while complex RTL text will still use HarfBuzz
    return TextComplexity::Simple;
}

ProcessingDirection UnifiedTextProcessor::AutoDetectDirection(const std::pmr::vector<uint16>& unicodes)
{
    int rtlCount = 0;
    int ltrCount = 0;

    for (uint16 unicode : unicodes) {
        if (IsRTLCharacter(unicode)) {
            rtlCount++;
        } else if (unicode >= 0x0041 && unicode <= 0x007A) { // Basic Latin
            ltrCount++;
        }
    }

    // Simple heuristic: if more than 30% of characters are RTL, treat as RTL
    if (rtlCount > 0 && (float)rtlCount / (rtlCount + ltrCount) > 0.3f) {
        return ProcessingDirection::RightToLeft;
    }

    return ProcessingDirection::LeftToRight;
}

ITextProcessor* UnifiedTextProcessor::SelectProcessor(TextComplexity complexity)
{
    switch (complexity) {
        case TextComplexity::Simple:
            return m_simpleProcessor.get();
            
        case TextComplexity::Complex:
#if USE_HARFBUZZ
            return m_harfBuzzProcessor.get();
#else
            // Fallback to simple processor if HarfBuzz not available
            return m_simpleProcessor.get();
#endif
    }
    
    return m_simpleProcessor.get(); // Default fallback
}

bool UnifiedTextProcessor::IsComplexScript(uint16 unicode)
{
    // Arabic: U+0600-U+06FF, U+0750-U+077F, U+08A0-U+08FF, U+FB50-U+FDFF, U+FE70-U+FEFF
    if ((unicode >= 0x0600 && unicode <= 0x06FF) ||
        (unicode >= 0x0750 && unicode <= 0x077F) ||
        (unicode >= 0x08A0 && unicode <= 0x08FF) ||
        (unicode >= 0xFB50 && unicode <= 0xFDFF) ||
        (unicode >= 0xFE70 && unicode <= 0xFEFF)) {
        return true;
    }

    // Devanagari: U+0900-U+097F
    if (unicode >= 0x0900 && unicode <= 0x097F) {
        return true;
    }

    // Bengali: U+0980-U+09FF
    if (unicode >= 0x0980 && unicode <= 0x09FF) {
        return true;
    }

    // Thai: U+0E00-U+0E7F
    if (unicode >= 0x0E00 && unicode <= 0x0E7F) {
        return true;
    }

    // Add more complex script ranges as needed
    return false;
}

bool UnifiedTextProcessor::IsRTLCharacter(uint16 unicode)
{
    // Hebrew: U+0590-U+05FF
    if (unicode >= 0x0590 && unicode <= 0x05FF) {
        return true;
    }

    // Arabic: U+0600-U+06FF, U+0750-U+077F, U+08A0-U+08FF
    if ((unicode >= 0x0600 && unicode <= 0x06FF) ||
        (unicode >= 0x0750 && unicode <= 0x077F) ||
        (unicode >= 0x08A0 && unicode <= 0x08FF)) {
        return true;
    }

    // Arabic Presentation Forms: U+FB50-U+FDFF, U+FE70-U+FEFF
    if ((unicode >= 0xFB50 && unicode <= 0xFDFF) ||
        (unicode >= 0xFE70 && unicode <= 0xFEFF)) {
        return true;
    }

    return false;
}

bool UnifiedTextProcessor::RequiresShaping(uint16 unicode)
{
    // Characters that require complex shaping (ligatures, combining marks, etc.)
    
    // Arabic shaping characters
    if (IsComplexScript(unicode)) {
        return true;
    }

    // Combining diacritical marks: U+0300-U+036F
    if (unicode >= 0x0300 && unicode <= 0x036F) {
        return true;
    }

    // Zero-width characters that affect shaping
    if (unicode == 0x200C || unicode == 0x200D) { // ZWNJ, ZWJ
        return true;
    }

    return false;
}

//////////////////////////////////////////////////////////////////////////
// SimpleTextProcessor Implementation
//////////////////////////////////////////////////////////////////////////

bool SimpleTextProcessor::CanHandle(TextComplexity complexity) const
{
    return complexity == TextComplexity::Simple;
}

bool SimpleTextProcessor::ProcessText(const TextProcessingParams& params)
{
    if (!params.generator) {
        return false;
    }

    // Process based on direction
    switch (params.direction) {
        case ProcessingDirection::LeftToRight:
            return ProcessLeftToRight(params);
            
        case ProcessingDirection::RightToLeft:
            return ProcessRightToLeft(params);
            
        case ProcessingDirection::Auto:
            // Should not reach here as direction should be resolved by UnifiedTextProcessor
            return ProcessLeftToRight(params);
    }

    return false;
}

bool SimpleTextProcessor::ProcessLeftToRight(const TextProcessingParams& params)
{
    auto* generator = params.generator;

    // Clear output containers using direct access
    auto& letters = generator->GetLetters();
    auto& lines = generator->GetLines();
    auto& meshData = generator->GetMeshData();
    letters.clear();
    lines.clear();
    meshData.Clear();

    // Initialize pen with offset using helper API
    generator->InitializePenWithOffset();

    // Create initial line using direct access
    auto& initialLine = lines.emplace_back(UniTextGenerator::Line{});
    initialLine.startIndex = 0;
    initialLine.maxFontSize = generator->GetBaseFontSize();

    // Get processing flags using public API
    const bool hasRichText = generator->HasRichText();
    const bool hasKerning = generator->HasKerning();

    // Get direct access to pen for efficient updates
    auto& pen = generator->GetPen();

    // Phase 1: Build logical lines without wrapping (pure character placement)
    const auto& unicodes = generator->GetUnicodes();
    for (int i = 0; i < unicodes.size(); i++)
    {
        // iterate and apply rich text tags
        if (hasRichText) generator->IterateRichTextTags(i);

        auto unicode = unicodes[i];
        if (hasKerning)
            pen.x += generator->GetKerning(pen.prevUnicode, unicode);

        pen.currentCharIdx = i;

        bool didFit = true;
        switch (unicode)
        {
            case '\t':
                didFit = InsertSpace(unicode, params, 2);
                break;
            case '\n':
                // new line - only create new lines for explicit \n
                didFit = InsertNewLine(params);
                break;
            case ' ':
                didFit = InsertSpace(unicode, params);
                break;
            case 0x200B:
                // Zero width space
                break;
            default:
                didFit = InsertCharacter(unicode, params, i);
                break;
        }
        if (!didFit) break;
        pen.prevUnicode = unicode;
    }

    return true;
}

bool SimpleTextProcessor::ProcessRightToLeft(const TextProcessingParams& params)
{
    auto* generator = params.generator;

    // Clear output containers using direct access
    auto& letters = generator->GetLetters();
    auto& lines = generator->GetLines();
    auto& meshData = generator->GetMeshData();
    letters.clear();
    lines.clear();
    meshData.Clear();

    // Initialize pen with offset using helper API
    generator->InitializePenWithOffset();

    // For RTL, we need to process the text in logical order first,
    // then apply visual reordering

    // Step 1: Process characters in logical order (same as LTR)
    if (!ProcessCharactersLogical(params)) {
        return false;
    }

    // Step 2: Apply RTL visual reordering
    ApplyRTLReordering(params);

    return true;
}

bool SimpleTextProcessor::ProcessCharactersLogical(const TextProcessingParams& params)
{
    auto* generator = params.generator;

    // Create initial line using direct access
    auto& lines = generator->GetLines();
    auto& initialLine = lines.emplace_back(UniTextGenerator::Line{});
    initialLine.startIndex = 0;
    initialLine.maxFontSize = generator->GetBaseFontSize();

    // Get processing flags using public API
    const bool hasRichText = generator->HasRichText();
    const bool hasKerning = generator->HasKerning();

    // Get direct access to pen for efficient updates
    auto& pen = generator->GetPen();

    // Process characters in logical order (same as LTR processing)
    const auto& unicodes = generator->GetUnicodes();
    for (int i = 0; i < unicodes.size(); i++)
    {
        // iterate and apply rich text tags
        if (hasRichText) generator->IterateRichTextTags(i);

        auto unicode = unicodes[i];
        if (hasKerning)
            pen.x += generator->GetKerning(pen.prevUnicode, unicode);

        pen.currentCharIdx = i;

        bool didFit = true;
        switch (unicode)
        {
            case '\t':
                didFit = InsertSpace(unicode, params, 2);
                break;
            case '\n':
                didFit = InsertNewLine(params);
                break;
            case ' ':
                didFit = InsertSpace(unicode, params);
                break;
            case 0x200B:
                // Zero width space
                break;
            default:
                didFit = InsertCharacter(unicode, params, i);
                break;
        }
        if (!didFit) break;
        pen.prevUnicode = unicode;
    }

    return true;
}

void SimpleTextProcessor::ApplyRTLReordering(const TextProcessingParams& params)
{
    auto* generator = params.generator;

    // Apply RTL reordering using direct access to data structures
    ApplyRTLReorderingDirect(generator);
}

void SimpleTextProcessor::ApplyRTLReorderingDirect(UniTextGenerator* generator)
{
    // Get direct access to data structures
    auto& letters = generator->GetLetters();
    auto& lines = generator->GetLines();
    auto& meshData = generator->GetMeshData();
    auto& pen = generator->GetPen();

    if (lines.empty() || letters.empty() || meshData.vertices.empty())
        return;

    // Process each line separately for RTL layout
    for (auto& line : lines)
    {
        if (line.startIndex >= line.endIndex || line.startIndex < 0 || line.endIndex >= static_cast<int>(letters.size()))
            continue;

        // Calculate the total width of characters in this line
        float lineWidth = 0.0f;
        for (int i = line.startIndex; i <= line.endIndex; ++i)
        {
            if (i < static_cast<int>(letters.size()))
            {
                lineWidth += letters[i].advance.x;
            }
        }

        // Calculate the starting X position for RTL (right edge of the line)
        // Get extents and pivot for proper RTL positioning
        const Vector2f& extents = generator->GetExtents();
        const Vector2f& pivot = generator->GetPivot();

        // Calculate the right edge position considering pivot
        float rightEdge = pen.offset.x + extents.x;
        float startX = rightEdge - lineWidth;
        float currentX = startX;

        // Reverse the visual order of characters in this line
        for (int i = line.endIndex; i >= line.startIndex; --i)
        {
            if (i < static_cast<int>(letters.size()) && letters[i].vertexIdx >= 0)
            {
                int vertexIdx = letters[i].vertexIdx;

                // Update vertex positions for this character (4 vertices per character)
                if (vertexIdx + 3 < static_cast<int>(meshData.vertices.size()))
                {
                    float charWidth = letters[i].advance.x;

                    // Update the 4 vertices for this character quad
                    meshData.vertices[vertexIdx].x = currentX;
                    meshData.vertices[vertexIdx + 1].x = currentX + charWidth;
                    meshData.vertices[vertexIdx + 2].x = currentX + charWidth;
                    meshData.vertices[vertexIdx + 3].x = currentX;

                    currentX += charWidth;
                }
            }
        }
    }
}

bool SimpleTextProcessor::InsertNewLine(const TextProcessingParams& params)
{
    auto* generator = params.generator;

    // Use public API to insert newline
    return generator->InsertNewLine();
}

bool SimpleTextProcessor::InsertSpace(uint16 unicode, const TextProcessingParams& params, int count)
{
    auto* generator = params.generator;

    // Use public API to insert space
    return generator->InsertSpace(unicode, count);
}

bool SimpleTextProcessor::InsertCharacter(uint16 unicode, const TextProcessingParams& params, int charIndex)
{
    auto* generator = params.generator;

    // Use public API to insert character
    return generator->InsertCharacter(unicode);
}

#if USE_HARFBUZZ
//////////////////////////////////////////////////////////////////////////
// HarfBuzzTextProcessor Implementation
//////////////////////////////////////////////////////////////////////////

HarfBuzzTextProcessor::HarfBuzzTextProcessor()
    : m_textShaper(std::make_unique<UniTextShaper>())
{
}

bool HarfBuzzTextProcessor::CanHandle(TextComplexity complexity) const
{
    return true; // HarfBuzz can handle both simple and complex text
}

bool HarfBuzzTextProcessor::ProcessText(const TextProcessingParams& params)
{
    if (!params.generator) {
        return false;
    }

    auto* generator = params.generator;

    // Clear output containers using direct access
    auto& letters = generator->GetLetters();
    auto& lines = generator->GetLines();
    auto& meshData = generator->GetMeshData();
    letters.clear();
    lines.clear();
    meshData.Clear();

    // Initialize HarfBuzz shaper
    if (!InitializeShaper(generator->GetFont(), generator->GetBaseFontSize())) {
        return false;
    }

    // Convert text to format expected by HarfBuzz
    const auto& unicodes = generator->GetUnicodes();
    std::vector<uint16_t> utf16_text;
    utf16_text.reserve(unicodes.size());
    for (uint16 unicode : unicodes) {
        utf16_text.push_back(unicode);
    }

    // Convert direction
    TextDirection direction = ConvertDirection(params.direction);

    // Shape the text
    auto result = m_textShaper->ShapeText(utf16_text.data(), utf16_text.size(), direction);

    if (!result.success) {
        return false;
    }

    // Initialize pen with offset using helper API
    generator->InitializePenWithOffset();

    // Create initial line using direct access
    auto& initialLine = lines.emplace_back(UniTextGenerator::Line{});
    initialLine.startIndex = 0;
    initialLine.maxFontSize = generator->GetBaseFontSize();

    // Process shaped glyphs directly using new API
    return ProcessShapedGlyphsDirect(generator, result, params.direction == ProcessingDirection::RightToLeft);
}

bool HarfBuzzTextProcessor::ProcessShapedGlyphsDirect(UniTextGenerator* generator, const ShapingResult& result, bool isRTL)
{
    // Get direct access to data structures
    auto& letters = generator->GetLetters();
    auto& lines = generator->GetLines();
    auto& meshData = generator->GetMeshData();
    auto& pen = generator->GetPen();

    // Process shaped glyphs and convert to UniText format
    float currentX = pen.offset.x;
    float currentY = pen.offset.y;

    // For RTL text, start from the right edge
    if (isRTL) {
        // Get extents and pivot for proper RTL positioning
        const Vector2f& extents = generator->GetExtents();
        const Vector2f& pivot = generator->GetPivot();

        // Calculate the right edge position and adjust for total text width
        float rightEdge = pen.offset.x + extents.x;
        currentX = rightEdge - result.total_advance_x;
    }

    for (size_t i = 0; i < result.glyphs.size(); ++i) {
        const auto& shapedGlyph = result.glyphs[i];

        // Add letter to the layout using direct access
        auto& letter = letters.emplace_back(UniTextGenerator::Letter{
            Vector2f(shapedGlyph.x_advance, shapedGlyph.y_advance),
            static_cast<int>(meshData.vertices.size()) // Vertex index
        });

        // Generate mesh data for the shaped glyph
        Vector2f glyphPos(currentX + shapedGlyph.x_offset, currentY + shapedGlyph.y_offset);
        Vector2f glyphSize(shapedGlyph.x_advance, generator->GetBaseFontSize()); // Simplified

        // Add vertices for the quad using direct access
        meshData.vertices.emplace_back(glyphPos.x, glyphPos.y);
        meshData.vertices.emplace_back(glyphPos.x + glyphSize.x, glyphPos.y);
        meshData.vertices.emplace_back(glyphPos.x + glyphSize.x, glyphPos.y + glyphSize.y);
        meshData.vertices.emplace_back(glyphPos.x, glyphPos.y + glyphSize.y);

        // Add placeholder UVs (would need actual glyph data)
        meshData.uvs.emplace_back(0.0f, 0.0f, 1.0f, 1.0f);
        meshData.uvs.emplace_back(0.0f, 0.0f, 1.0f, 1.0f);
        meshData.uvs.emplace_back(0.0f, 0.0f, 1.0f, 1.0f);
        meshData.uvs.emplace_back(0.0f, 0.0f, 1.0f, 1.0f);

        // Add colors using direct access
        // TODO: Get current color from generator (need a getter for base color)
        Color currentColor = Color::white; // Placeholder
        meshData.colors.emplace_back(currentColor);
        meshData.colors.emplace_back(currentColor);
        meshData.colors.emplace_back(currentColor);
        meshData.colors.emplace_back(currentColor);

        currentX += shapedGlyph.x_advance;
        currentY += shapedGlyph.y_advance;
    }

    // Finalize the line using direct access
    auto& line = lines.back();
    line.endIndex = static_cast<int>(letters.size()) - 1;
    line.width = result.total_advance_x;

    return true;
}

bool HarfBuzzTextProcessor::InitializeShaper(UniFont* font, int fontSize)
{
    if (!font || !m_textShaper) {
        return false;
    }

    if (m_textShaper->IsInitialized()) {
        return true;
    }

    // Get font data from UniFont
    const unsigned char* fontData = nullptr;
    unsigned long fontDataSize = 0;

    if (!font->GetFontData(&fontData, &fontDataSize) || !fontData || fontDataSize == 0) {
        return false;
    }

    // Initialize HarfBuzz with the font data
    return m_textShaper->Initialize(fontData, fontDataSize, static_cast<float>(fontSize));
}

TextDirection HarfBuzzTextProcessor::ConvertDirection(ProcessingDirection direction) const
{
    switch (direction) {
        case ProcessingDirection::LeftToRight:
            return TextDirection::LeftToRight;
        case ProcessingDirection::RightToLeft:
            return TextDirection::RightToLeft;
        case ProcessingDirection::Auto:
            return TextDirection::LeftToRight; // Default
    }
    return TextDirection::LeftToRight;
}
#endif

NAMESPACE_END
