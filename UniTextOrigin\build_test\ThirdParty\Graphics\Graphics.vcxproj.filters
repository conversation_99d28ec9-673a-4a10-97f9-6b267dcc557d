﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\Private\Graphics.cpp">
      <Filter>Private</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\Private\Graphics_D3D11.cpp">
      <Filter>Private</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\Private\Graphics_D3D12.cpp">
      <Filter>Private</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\Private\Graphics_OpenGLES.cpp">
      <Filter>Private</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\Private\Graphics_Vulkan.cpp">
      <Filter>Private</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\Private\Graphics_Meta.mm">
      <Filter>Private</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\Private\ShaderManager.cpp">
      <Filter>Private</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\gl3w\gl3w.c">
      <Filter>gl3w</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\PlatformMacro.h">
      <Filter>Public</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\Graphics.hpp">
      <Filter>Public</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\ShaderManager.hpp">
      <Filter>Public</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\gl3w\gl3w.h">
      <Filter>gl3w</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\gl3w\glcorearb.h">
      <Filter>gl3w</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\Graphics\ARCHITECTURE_DESIGN.md">
      <Filter>Documentation</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Documentation">
      <UniqueIdentifier>{CA8EBEBE-0F82-3AB7-99A8-92518BDC3199}</UniqueIdentifier>
    </Filter>
    <Filter Include="Private">
      <UniqueIdentifier>{92B8BFD2-C80E-3E8A-9E8E-237A585F3C44}</UniqueIdentifier>
    </Filter>
    <Filter Include="Public">
      <UniqueIdentifier>{A148DAB9-0AC8-31E0-8C38-29F43BEDB655}</UniqueIdentifier>
    </Filter>
    <Filter Include="gl3w">
      <UniqueIdentifier>{4F5C7D9F-A1A5-306E-BFB3-1BC2FF7233A6}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
