﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\gl3w\gl3w.c">
      <Filter>gl3w</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\RenderAPI.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\RenderAPI_D3D11.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\RenderAPI_D3D12.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\RenderAPI_OpenGLCoreES.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\RenderAPI_Vulkan.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\UnityPluginEntry.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\gl3w\gl3w.h">
      <Filter>gl3w</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\gl3w\glcorearb.h">
      <Filter>gl3w</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\Unity\IUnityGraphics.h">
      <Filter>Unity</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\Unity\IUnityGraphicsD3D9.h">
      <Filter>Unity</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\Unity\IUnityGraphicsD3D11.h">
      <Filter>Unity</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\Unity\IUnityGraphicsD3D12.h">
      <Filter>Unity</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\Unity\IUnityGraphicsMetal.h">
      <Filter>Unity</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\Unity\IUnityGraphicsVulkan.h">
      <Filter>Unity</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\Unity\IUnityInterface.h">
      <Filter>Unity</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\Unity\IUnityProfiler.h">
      <Filter>Unity</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\PlatformBase.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source\RenderAPI.h" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Unity">
      <UniqueIdentifier>{18B16B7A-AABB-3A96-A881-001D5595DB50}</UniqueIdentifier>
    </Filter>
    <Filter Include="gl3w">
      <UniqueIdentifier>{4F5C7D9F-A1A5-306E-BFB3-1BC2FF7233A6}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
