# CMake generation dependency list for this directory.
C:/Program Files/CMake/share/cmake-4.0/Modules/BasicConfigVersion-SameMajorVersion.cmake.in
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCCompiler.cmake.in
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCCompilerABI.c
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCInformation.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCompilerIdDetection.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDependentOption.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakePackageConfigHelpers.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseImplicitIncludeInfo.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseImplicitLinkInfo.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseLibraryArchitecture.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CPack.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CPackComponent.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CheckIncludeFile.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/ADSP-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/ARMCC-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/ARMClang-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/AppleClang-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Borland-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Bruce-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Compaq-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Cray-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/CrayClang-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Embarcadero-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Fujitsu-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/GHS-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/GNU-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/HP-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IAR-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Intel-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/LCC-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/MSVC-C.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/MSVC-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/MSVC.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/NVHPC-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/NVIDIA-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/OrangeC-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/PGI-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/PathScale-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/SCO-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/SDCC-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/SunPro-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/TI-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/TIClang-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Tasking-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Watcom-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/XL-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/XLClang-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/zOS-C-DetermineCompiler.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/CompilerId/VS-10.vcxproj.in
C:/Program Files/CMake/share/cmake-4.0/Modules/FindBZip2.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/FindPNG.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/FindPackageMessage.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/FindPkgConfig.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/FindZLIB.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/GNUInstallDirs.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Linker/MSVC-C.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Linker/MSVC.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-C.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Windows-MSVC-C.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/SelectLibraryConfigurations.cmake
C:/Program Files/CMake/share/cmake-4.0/Modules/WriteBasicConfigVersionFile.cmake
C:/Program Files/CMake/share/cmake-4.0/Templates/CPackConfig.cmake.in
E:/trunk_base/Young/UniTextGit/UniTextOrigin/ThirdParty/freetype/CMakeLists.txt
E:/trunk_base/Young/UniTextGit/UniTextOrigin/ThirdParty/freetype/builds/cmake/FindBrotliDec.cmake
E:/trunk_base/Young/UniTextGit/UniTextOrigin/ThirdParty/freetype/builds/cmake/FindHarfBuzz.cmake
E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/CMakeFiles/4.0.0-rc3/CMakeCCompiler.cmake
