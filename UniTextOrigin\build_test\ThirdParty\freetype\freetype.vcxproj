﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B66FD415-5A13-380D-A168-FD34AC999199}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>freetype</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">freetype.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">freetyped</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">freetype.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">freetype</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">freetype.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">freetype</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">freetype.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">freetype</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;FT2_BUILD_LIBRARY;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;FT2_BUILD_LIBRARY;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FT2_BUILD_LIBRARY;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FT2_BUILD_LIBRARY;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FT2_BUILD_LIBRARY;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FT2_BUILD_LIBRARY;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FT2_BUILD_LIBRARY;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FT2_BUILD_LIBRARY;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include\freetype\config;C:\Strawberry\c\include\harfbuzz;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/trunk_base/Young/UniTextGit/UniTextOrigin/ThirdParty/freetype/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test --check-stamp-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/ThirdParty/freetype/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CPack.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CPackComponent.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-4.0\Modules\FindBZip2.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPNG.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Templates\CPackConfig.cmake.in;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\cmake\FindBrotliDec.cmake;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\cmake\FindHarfBuzz.cmake;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\CMakeFiles\4.0.0-rc3\CMakeCCompiler.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/trunk_base/Young/UniTextGit/UniTextOrigin/ThirdParty/freetype/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test --check-stamp-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/ThirdParty/freetype/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CPack.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CPackComponent.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-4.0\Modules\FindBZip2.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPNG.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Templates\CPackConfig.cmake.in;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\cmake\FindBrotliDec.cmake;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\cmake\FindHarfBuzz.cmake;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\CMakeFiles\4.0.0-rc3\CMakeCCompiler.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/trunk_base/Young/UniTextGit/UniTextOrigin/ThirdParty/freetype/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test --check-stamp-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/ThirdParty/freetype/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CPack.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CPackComponent.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-4.0\Modules\FindBZip2.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPNG.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Templates\CPackConfig.cmake.in;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\cmake\FindBrotliDec.cmake;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\cmake\FindHarfBuzz.cmake;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\CMakeFiles\4.0.0-rc3\CMakeCCompiler.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/trunk_base/Young/UniTextGit/UniTextOrigin/ThirdParty/freetype/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test --check-stamp-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/ThirdParty/freetype/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CPack.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CPackComponent.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-4.0\Modules\FindBZip2.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPNG.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Templates\CPackConfig.cmake.in;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\cmake\FindBrotliDec.cmake;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\cmake\FindHarfBuzz.cmake;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\CMakeFiles\4.0.0-rc3\CMakeCCompiler.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\freetype.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftadvanc.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftbbox.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftbdf.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftbitmap.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftbzip2.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftcache.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftchapters.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftcid.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftcolor.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftdriver.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\fterrdef.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\fterrors.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftfntfmt.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftgasp.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftglyph.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftgxval.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftgzip.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftimage.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftincrem.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftlcdfil.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftlist.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftlogging.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftlzw.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftmac.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftmm.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftmodapi.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftmoderr.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftotval.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftoutln.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftparams.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftpfr.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftrender.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftsizes.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftsnames.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftstroke.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftsynth.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftsystem.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\fttrigon.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\fttypes.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftwinfnt.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\otsvg.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\t1tables.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ttnameid.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\tttables.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\tttags.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\ft2build.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftconfig.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftheader.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftmodule.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftoption.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftstdlib.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\integer-types.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\mac-support.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\public-macros.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\autohint.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\cffotypes.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\cfftypes.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\compiler-macros.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftcalc.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftdebug.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftdrv.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftgloadr.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\fthash.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftmemory.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftobjs.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftpsprop.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftrfork.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftserv.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftstream.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\fttrace.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftvalid.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\psaux.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\pshints.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\sfnt.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\svginterface.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\t1types.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\tttypes.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\wofftypes.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\autofit\autofit.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftbase.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftbbox.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftbdf.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftbitmap.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftcid.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftfstype.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftgasp.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftglyph.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftgxval.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftinit.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftmm.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftotval.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftpatent.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftpfr.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftstroke.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftsynth.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\fttype1.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftwinfnt.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\bdf\bdf.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\bzip2\ftbzip2.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\cache\ftcache.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\cff\cff.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\cid\type1cid.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\gzip\ftgzip.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\lzw\ftlzw.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\pcf\pcf.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\pfr\pfr.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\psaux\psaux.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\pshinter\pshinter.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\psnames\psnames.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\raster\raster.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\sdf\sdf.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\vector_sdf\vsdf.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\sfnt\sfnt.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\smooth\smooth.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\svg\svg.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\truetype\truetype.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\type1\type1.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\type42\type42.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\winfonts\winfnt.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\windows\ftsystem.c" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\windows\ftdebug.c" />
    <ResourceCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftver.rc" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ZERO_CHECK.vcxproj">
      <Project>{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>