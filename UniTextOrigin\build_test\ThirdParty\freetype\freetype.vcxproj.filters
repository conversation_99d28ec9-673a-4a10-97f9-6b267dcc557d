﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\autofit\autofit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftbase.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftbbox.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftbdf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftbitmap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftcid.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftfstype.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftgasp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftglyph.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftgxval.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftinit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftmm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftotval.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftpatent.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftpfr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftstroke.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftsynth.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\fttype1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftwinfnt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\bdf\bdf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\bzip2\ftbzip2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\cache\ftcache.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\cff\cff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\cid\type1cid.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\gzip\ftgzip.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\lzw\ftlzw.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\pcf\pcf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\pfr\pfr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\psaux\psaux.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\pshinter\pshinter.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\psnames\psnames.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\raster\raster.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\sdf\sdf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\vector_sdf\vsdf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\sfnt\sfnt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\smooth\smooth.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\svg\svg.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\truetype\truetype.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\type1\type1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\type42\type42.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\winfonts\winfnt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\windows\ftsystem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\builds\windows\ftdebug.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\freetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftadvanc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftbbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftbdf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftbitmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftbzip2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftcache.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftchapters.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftcid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftcolor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftdriver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\fterrdef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\fterrors.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftfntfmt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftgasp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftglyph.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftgxval.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftgzip.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftincrem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftlcdfil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftlist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftlogging.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftlzw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftmac.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftmm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftmodapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftmoderr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftotval.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftoutln.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftparams.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftpfr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftrender.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftsizes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftsnames.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftstroke.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftsynth.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftsystem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\fttrigon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\fttypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ftwinfnt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\otsvg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\t1tables.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\ttnameid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\tttables.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\tttags.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\ft2build.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftheader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftmodule.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftoption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\ftstdlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\integer-types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\mac-support.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\config\public-macros.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\autohint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\cffotypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\cfftypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\compiler-macros.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftcalc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftdebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftdrv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftgloadr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\fthash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftmemory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftobjs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftpsprop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftrfork.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftserv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftstream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\fttrace.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\ftvalid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\psaux.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\pshints.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\sfnt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\svginterface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\t1types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\tttypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include\freetype\internal\wofftypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\src\base\ftver.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{B9CBAC62-8587-3A64-8B52-A21EAF5B6A57}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{0421A569-17EE-36A7-AA10-ADA7AA17A20D}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
