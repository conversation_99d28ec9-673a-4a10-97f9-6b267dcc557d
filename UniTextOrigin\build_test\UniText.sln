﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{E6237ABD-307F-3294-BC75-051FC7264B0B}"
	ProjectSection(ProjectDependencies) = postProject
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3} = {A6931B0E-DD21-3927-9D36-1A85EF54D7C3}
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0} = {5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47} = {A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}
		{B66FD415-5A13-380D-A168-FD34AC999199} = {B66FD415-5A13-380D-A168-FD34AC999199}
		{64756D41-BCFC-3028-BF0A-AEAFE513ED78} = {64756D41-BCFC-3028-BF0A-AEAFE513ED78}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Graphics", "ThirdParty\Graphics\Graphics.vcxproj", "{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}"
	ProjectSection(ProjectDependencies) = postProject
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47} = {A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{963B0EF1-CEE7-3819-ACCE-8EF7BFB7586D}"
	ProjectSection(ProjectDependencies) = postProject
		{E6237ABD-307F-3294-BC75-051FC7264B0B} = {E6237ABD-307F-3294-BC75-051FC7264B0B}
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47} = {A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "NativeRenderPlugin", "ThirdParty\NativeRenderPlugin\NativeRenderPlugin.vcxproj", "{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}"
	ProjectSection(ProjectDependencies) = postProject
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47} = {A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{008607DA-FDEE-36B5-82E4-4E026C2246A0}"
	ProjectSection(ProjectDependencies) = postProject
		{E6237ABD-307F-3294-BC75-051FC7264B0B} = {E6237ABD-307F-3294-BC75-051FC7264B0B}
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47} = {A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "freetype", "ThirdParty\freetype\freetype.vcxproj", "{B66FD415-5A13-380D-A168-FD34AC999199}"
	ProjectSection(ProjectDependencies) = postProject
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47} = {A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "libUniText", "UniText\libUniText.vcxproj", "{64756D41-BCFC-3028-BF0A-AEAFE513ED78}"
	ProjectSection(ProjectDependencies) = postProject
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3} = {A6931B0E-DD21-3927-9D36-1A85EF54D7C3}
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0} = {5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47} = {A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}
		{B66FD415-5A13-380D-A168-FD34AC999199} = {B66FD415-5A13-380D-A168-FD34AC999199}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E6237ABD-307F-3294-BC75-051FC7264B0B}.Debug|x64.ActiveCfg = Debug|x64
		{E6237ABD-307F-3294-BC75-051FC7264B0B}.Debug|x64.Build.0 = Debug|x64
		{E6237ABD-307F-3294-BC75-051FC7264B0B}.Release|x64.ActiveCfg = Release|x64
		{E6237ABD-307F-3294-BC75-051FC7264B0B}.Release|x64.Build.0 = Release|x64
		{E6237ABD-307F-3294-BC75-051FC7264B0B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E6237ABD-307F-3294-BC75-051FC7264B0B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E6237ABD-307F-3294-BC75-051FC7264B0B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E6237ABD-307F-3294-BC75-051FC7264B0B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}.Debug|x64.ActiveCfg = Debug|x64
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}.Debug|x64.Build.0 = Debug|x64
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}.Release|x64.ActiveCfg = Release|x64
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}.Release|x64.Build.0 = Release|x64
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{963B0EF1-CEE7-3819-ACCE-8EF7BFB7586D}.Debug|x64.ActiveCfg = Debug|x64
		{963B0EF1-CEE7-3819-ACCE-8EF7BFB7586D}.Release|x64.ActiveCfg = Release|x64
		{963B0EF1-CEE7-3819-ACCE-8EF7BFB7586D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{963B0EF1-CEE7-3819-ACCE-8EF7BFB7586D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}.Debug|x64.ActiveCfg = Debug|x64
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}.Debug|x64.Build.0 = Debug|x64
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}.Release|x64.ActiveCfg = Release|x64
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}.Release|x64.Build.0 = Release|x64
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{008607DA-FDEE-36B5-82E4-4E026C2246A0}.Debug|x64.ActiveCfg = Debug|x64
		{008607DA-FDEE-36B5-82E4-4E026C2246A0}.Release|x64.ActiveCfg = Release|x64
		{008607DA-FDEE-36B5-82E4-4E026C2246A0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{008607DA-FDEE-36B5-82E4-4E026C2246A0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}.Debug|x64.ActiveCfg = Debug|x64
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}.Debug|x64.Build.0 = Debug|x64
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}.Release|x64.ActiveCfg = Release|x64
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}.Release|x64.Build.0 = Release|x64
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{B66FD415-5A13-380D-A168-FD34AC999199}.Debug|x64.ActiveCfg = Debug|x64
		{B66FD415-5A13-380D-A168-FD34AC999199}.Debug|x64.Build.0 = Debug|x64
		{B66FD415-5A13-380D-A168-FD34AC999199}.Release|x64.ActiveCfg = Release|x64
		{B66FD415-5A13-380D-A168-FD34AC999199}.Release|x64.Build.0 = Release|x64
		{B66FD415-5A13-380D-A168-FD34AC999199}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B66FD415-5A13-380D-A168-FD34AC999199}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B66FD415-5A13-380D-A168-FD34AC999199}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B66FD415-5A13-380D-A168-FD34AC999199}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{64756D41-BCFC-3028-BF0A-AEAFE513ED78}.Debug|x64.ActiveCfg = Debug|x64
		{64756D41-BCFC-3028-BF0A-AEAFE513ED78}.Debug|x64.Build.0 = Debug|x64
		{64756D41-BCFC-3028-BF0A-AEAFE513ED78}.Release|x64.ActiveCfg = Release|x64
		{64756D41-BCFC-3028-BF0A-AEAFE513ED78}.Release|x64.Build.0 = Release|x64
		{64756D41-BCFC-3028-BF0A-AEAFE513ED78}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{64756D41-BCFC-3028-BF0A-AEAFE513ED78}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{64756D41-BCFC-3028-BF0A-AEAFE513ED78}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{64756D41-BCFC-3028-BF0A-AEAFE513ED78}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {24CEE324-2943-3B14-8EF6-81F019024916}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
