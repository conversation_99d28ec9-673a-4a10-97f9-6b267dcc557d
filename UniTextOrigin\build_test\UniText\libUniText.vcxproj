﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{64756D41-BCFC-3028-BF0A-AEAFE513ED78}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>libUniText</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">libUniText.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">libUniText</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">libUniText.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">libUniText</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">libUniText.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">libUniText</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">libUniText.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">libUniText</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;UNITY=1;UNITEXT_EXPORTS;USE_FREETYPE=1;USE_ROUND_POSITION=1;USE_THREAD_POOL=0;USE_HARFBUZZ=1;USE_LINEAR_PACK=0;USE_FREETYPE_MEMORY=0;CMAKE_INTDIR="Debug";libUniText_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;UNITY=1;UNITEXT_EXPORTS;USE_FREETYPE=1;USE_ROUND_POSITION=1;USE_THREAD_POOL=0;USE_HARFBUZZ=1;USE_LINEAR_PACK=0;USE_FREETYPE_MEMORY=0;CMAKE_INTDIR=\"Debug\";libUniText_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/Debug/exports.def E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/Debug//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>..\ThirdParty\freetype\Debug\freetyped.lib;C:\Strawberry\c\lib\libharfbuzz.a;..\ThirdParty\Graphics\Debug\Graphics.lib;..\ThirdParty\NativeRenderPlugin\Debug\NativeRenderPlugin.lib;opengl32.lib;glu32.lib;d3d11.lib;d3d12.lib;dxgi.lib;d3dcompiler.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/Debug/libUniText.lib</ImportLibrary>
      <ModuleDefinitionFile>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/Debug/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/Debug/libUniText.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNITY=1;UNITEXT_EXPORTS;USE_FREETYPE=1;USE_ROUND_POSITION=1;USE_THREAD_POOL=0;USE_HARFBUZZ=1;USE_LINEAR_PACK=0;USE_FREETYPE_MEMORY=0;CMAKE_INTDIR="Release";libUniText_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNITY=1;UNITEXT_EXPORTS;USE_FREETYPE=1;USE_ROUND_POSITION=1;USE_THREAD_POOL=0;USE_HARFBUZZ=1;USE_LINEAR_PACK=0;USE_FREETYPE_MEMORY=0;CMAKE_INTDIR=\"Release\";libUniText_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/Release/exports.def E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/Release//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>..\ThirdParty\freetype\Release\freetype.lib;C:\Strawberry\c\lib\libharfbuzz.a;..\ThirdParty\Graphics\Release\Graphics.lib;..\ThirdParty\NativeRenderPlugin\Release\NativeRenderPlugin.lib;opengl32.lib;glu32.lib;d3d11.lib;d3d12.lib;dxgi.lib;d3dcompiler.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/Release/libUniText.lib</ImportLibrary>
      <ModuleDefinitionFile>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/Release/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/Release/libUniText.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNITY=1;UNITEXT_EXPORTS;USE_FREETYPE=1;USE_ROUND_POSITION=1;USE_THREAD_POOL=0;USE_HARFBUZZ=1;USE_LINEAR_PACK=0;USE_FREETYPE_MEMORY=0;CMAKE_INTDIR="MinSizeRel";libUniText_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNITY=1;UNITEXT_EXPORTS;USE_FREETYPE=1;USE_ROUND_POSITION=1;USE_THREAD_POOL=0;USE_HARFBUZZ=1;USE_LINEAR_PACK=0;USE_FREETYPE_MEMORY=0;CMAKE_INTDIR=\"MinSizeRel\";libUniText_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/MinSizeRel/exports.def E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/MinSizeRel//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>..\ThirdParty\freetype\MinSizeRel\freetype.lib;C:\Strawberry\c\lib\libharfbuzz.a;..\ThirdParty\Graphics\MinSizeRel\Graphics.lib;..\ThirdParty\NativeRenderPlugin\MinSizeRel\NativeRenderPlugin.lib;opengl32.lib;glu32.lib;d3d11.lib;d3d12.lib;dxgi.lib;d3dcompiler.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/MinSizeRel/libUniText.lib</ImportLibrary>
      <ModuleDefinitionFile>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/MinSizeRel/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/MinSizeRel/libUniText.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNITY=1;UNITEXT_EXPORTS;USE_FREETYPE=1;USE_ROUND_POSITION=1;USE_THREAD_POOL=0;USE_HARFBUZZ=1;USE_LINEAR_PACK=0;USE_FREETYPE_MEMORY=0;CMAKE_INTDIR="RelWithDebInfo";libUniText_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNITY=1;UNITEXT_EXPORTS;USE_FREETYPE=1;USE_ROUND_POSITION=1;USE_THREAD_POOL=0;USE_HARFBUZZ=1;USE_LINEAR_PACK=0;USE_FREETYPE_MEMORY=0;CMAKE_INTDIR=\"RelWithDebInfo\";libUniText_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\freetype\include;C:\Strawberry\c\include\harfbuzz;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty\NativeRenderPlugin\Source;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\..\ThirdParty;E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\freetype\include;E:\trunk_base\Young\UniTextGit\UniTextOrigin\ThirdParty\NativeRenderPlugin\Source;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/RelWithDebInfo/exports.def E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/RelWithDebInfo//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>..\ThirdParty\freetype\RelWithDebInfo\freetype.lib;C:\Strawberry\c\lib\libharfbuzz.a;..\ThirdParty\Graphics\RelWithDebInfo\Graphics.lib;..\ThirdParty\NativeRenderPlugin\RelWithDebInfo\NativeRenderPlugin.lib;opengl32.lib;glu32.lib;d3d11.lib;d3d12.lib;dxgi.lib;d3dcompiler.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/RelWithDebInfo/libUniText.lib</ImportLibrary>
      <ModuleDefinitionFile>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/libUniText.dir/RelWithDebInfo/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/RelWithDebInfo/libUniText.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/trunk_base/Young/UniTextGit/UniTextOrigin/UniText/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test --check-stamp-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/trunk_base/Young/UniTextGit/UniTextOrigin/UniText/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test --check-stamp-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/trunk_base/Young/UniTextGit/UniTextOrigin/UniText/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test --check-stamp-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/trunk_base/Young/UniTextGit/UniTextOrigin/UniText/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test --check-stamp-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\UniText\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\IUniGlyphPacker.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\MaxRectsGlyphPacker.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\GridGlyphPacker.hpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\ShelfGlyphPacker.hpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\MaxRectsGlyphPacker.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\UniGlyphPacker.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\IGlyphRenderer.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniSDF_Renderer.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniSDF_Shader.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniFT_Renderer.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniSDF_Renderer.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniFT_Renderer.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\IUniPostProcessor.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\SDFGenerator.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\UniPostProcessor.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\SDFGenerator.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Windows\WindowsFontFallback.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Android\AndroidFontFallback.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Apple\AppleFontFallback.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Default\DefaultFontFallback.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Windows\WindowsFontFallback.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Android\AndroidFontFallback.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Apple\AppleFontFallback.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Default\DefaultFontFallback.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\Unicode\Unicode.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\Unicode\Unicode.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\Allocator.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\BS_thread_pool_light.hpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\flat_hash_map.hpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\RectUtility.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\TextUtility.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\FreeType\UniFontFreeType.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\FreeType\UniFontFreeType.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\TextShaping\UniTextShaper.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\TextShaping\UniTextShaper.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextProcessor.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextProcessor.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Custom\UniCustomFont.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Custom\UniCustomFont.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFont.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontAtlasEntry.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontFallback.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\IUniFontImpl.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFont.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontAtlasEntry.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontFallback.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGlyphData.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniMacro.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniPlatform.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGUID.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniObject.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGlyphData.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniPlatform.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGUID.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniObject.cpp" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniText.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextAPIExport.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGlobal.h" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextAPIExport.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGlobal.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ZERO_CHECK.vcxproj">
      <Project>{A8F10A34-C4B1-3C24-A2EC-DC40DB6EED47}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\Graphics\Graphics.vcxproj">
      <Project>{A6931B0E-DD21-3927-9D36-1A85EF54D7C3}</Project>
      <Name>Graphics</Name>
    </ProjectReference>
    <ProjectReference Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\NativeRenderPlugin\NativeRenderPlugin.vcxproj">
      <Project>{5EC6C5D1-0195-36BC-AAB0-6CFD35227CA0}</Project>
      <Name>NativeRenderPlugin</Name>
    </ProjectReference>
    <ProjectReference Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\build_test\ThirdParty\freetype\freetype.vcxproj">
      <Project>{B66FD415-5A13-380D-A168-FD34AC999199}</Project>
      <Name>freetype</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>