﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\MaxRectsGlyphPacker.cpp">
      <Filter>Font\GlyphPacker</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\UniGlyphPacker.cpp">
      <Filter>Font\GlyphPacker</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniSDF_Renderer.cpp">
      <Filter>Font\GlyphRender</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniFT_Renderer.cpp">
      <Filter>Font\GlyphRender</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\UniPostProcessor.cpp">
      <Filter>Font\Postprocessor</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\SDFGenerator.cpp">
      <Filter>Font\Postprocessor</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Windows\WindowsFontFallback.cpp">
      <Filter>Font\System</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Android\AndroidFontFallback.cpp">
      <Filter>Font\System</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Apple\AppleFontFallback.cpp">
      <Filter>Font\System</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Default\DefaultFontFallback.cpp">
      <Filter>Font\System</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\Unicode\Unicode.cpp">
      <Filter>Common\Unicode</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\FreeType\UniFontFreeType.cpp">
      <Filter>Font\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\TextShaping\UniTextShaper.cpp">
      <Filter>Font\TextShaping</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextProcessor.cpp">
      <Filter>TextProcessor</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Custom\UniCustomFont.cpp">
      <Filter>Font\Custom</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFont.cpp">
      <Filter>Font</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontAtlasEntry.cpp">
      <Filter>Font</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontFallback.cpp">
      <Filter>Font</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGlyphData.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniPlatform.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGUID.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniObject.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextAPIExport.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp" />
    <ClCompile Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGlobal.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\IUniGlyphPacker.h">
      <Filter>Font\GlyphPacker</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\MaxRectsGlyphPacker.h">
      <Filter>Font\GlyphPacker</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\GridGlyphPacker.hpp">
      <Filter>Font\GlyphPacker</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\ShelfGlyphPacker.hpp">
      <Filter>Font\GlyphPacker</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\IGlyphRenderer.h">
      <Filter>Font\GlyphRender</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniSDF_Renderer.h">
      <Filter>Font\GlyphRender</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniSDF_Shader.h">
      <Filter>Font\GlyphRender</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniFT_Renderer.h">
      <Filter>Font\GlyphRender</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\IUniPostProcessor.h">
      <Filter>Font\Postprocessor</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\SDFGenerator.h">
      <Filter>Font\Postprocessor</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Windows\WindowsFontFallback.h">
      <Filter>Font\System</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Android\AndroidFontFallback.h">
      <Filter>Font\System</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Apple\AppleFontFallback.h">
      <Filter>Font\System</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Default\DefaultFontFallback.h">
      <Filter>Font\System</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\Unicode\Unicode.h">
      <Filter>Common\Unicode</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\Allocator.h">
      <Filter>Utility</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\BS_thread_pool_light.hpp">
      <Filter>Utility</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\flat_hash_map.hpp">
      <Filter>Utility</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\RectUtility.h">
      <Filter>Utility</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Utility\TextUtility.h">
      <Filter>Utility</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\FreeType\UniFontFreeType.h">
      <Filter>Font\FreeType</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\TextShaping\UniTextShaper.h">
      <Filter>Font\TextShaping</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextProcessor.h">
      <Filter>TextProcessor</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Custom\UniCustomFont.h">
      <Filter>Font\Custom</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFont.h">
      <Filter>Font</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontAtlasEntry.h">
      <Filter>Font</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontFallback.h">
      <Filter>Font</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\IUniFontImpl.h">
      <Filter>Font</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGlyphData.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniMacro.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniPlatform.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGUID.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniObject.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniText.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextAPIExport.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.h" />
    <ClInclude Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGlobal.h" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Common">
      <UniqueIdentifier>{6ED852E5-A958-3E0C-8581-9718856DB924}</UniqueIdentifier>
    </Filter>
    <Filter Include="Common\Unicode">
      <UniqueIdentifier>{823034A5-1A56-36F0-BF85-DADA1300C99F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Font">
      <UniqueIdentifier>{4A0CD98C-8253-3C8C-8BB0-AB4A719AA0D3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Font\Custom">
      <UniqueIdentifier>{A68BC375-633F-39D4-94F6-1649EE48C784}</UniqueIdentifier>
    </Filter>
    <Filter Include="Font\FreeType">
      <UniqueIdentifier>{F067462F-EEDD-305C-8B3E-D2D3C6917E36}</UniqueIdentifier>
    </Filter>
    <Filter Include="Font\GlyphPacker">
      <UniqueIdentifier>{ACB5BEE0-A84C-337B-BE11-1D6D8EAEB652}</UniqueIdentifier>
    </Filter>
    <Filter Include="Font\GlyphRender">
      <UniqueIdentifier>{9AA4F689-9ECC-3EC1-9EFF-CE209B8AFC1E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Font\Postprocessor">
      <UniqueIdentifier>{F4141412-DC42-3082-976D-AC04ED7C7120}</UniqueIdentifier>
    </Filter>
    <Filter Include="Font\System">
      <UniqueIdentifier>{618D065A-C7FD-3E33-BBB1-A8DE29FC1451}</UniqueIdentifier>
    </Filter>
    <Filter Include="Font\TextShaping">
      <UniqueIdentifier>{26E9AE91-1998-326A-861C-00ACFC552AF2}</UniqueIdentifier>
    </Filter>
    <Filter Include="TextProcessor">
      <UniqueIdentifier>{447DCCEA-3358-3B69-81D4-7CAD7B6A38A4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Utility">
      <UniqueIdentifier>{AC439B8B-DDDC-3F2A-865E-6124BAE5794B}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
