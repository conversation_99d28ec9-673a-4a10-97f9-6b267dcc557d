^E:\TRUNK_BASE\YOUNG\UNITEXTGIT\UNITEXTORIGIN\BUILD_TEST\CMAKEFILES\84BC2865F5620E0FCE0F5A40884A5060\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_test/UniText.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
