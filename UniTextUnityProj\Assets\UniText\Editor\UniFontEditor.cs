﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(UniFont))]
public class UniFontEditor : Editor
{
    private SerializedProperty fontSize;
    private SerializedProperty minFontSize;
    private SerializedProperty maxFontSize;
    private SerializedProperty padding;
    private SerializedProperty fontPath;
    private SerializedProperty renderMode;

    private bool m_PropertyDirty = false;
    UniFont targetUniFont = null;
    private void OnEnable()
    {
        targetUniFont = target as UniFont;

        // Link the properties
        fontSize = serializedObject.FindProperty("m_FontSize");
        minFontSize = serializedObject.FindProperty("m_MinFontSize");
        maxFontSize = serializedObject.FindProperty("m_MaxFontSize");
        padding = serializedObject.FindProperty("m_Padding");
        fontPath = serializedObject.FindProperty("m_FontPath");
        renderMode = serializedObject.FindProperty("m_RenderMode");
    }

    public override void OnInspectorGUI()
    {
        //_ = DrawDefaultInspector();
        //base.OnInspectorGUI();

        serializedObject.Update();
        EditorGUI.BeginChangeCheck();

        EditorGUILayout.PropertyField(fontPath);
        EditorGUILayout.PropertyField(fontSize);
        EditorGUILayout.PropertyField(minFontSize);
        EditorGUILayout.PropertyField(maxFontSize);
        EditorGUILayout.PropertyField(padding);
        EditorGUILayout.PropertyField(renderMode);

        if (EditorGUI.EndChangeCheck()) m_PropertyDirty = true;

        EditorGUI.BeginDisabledGroup(!m_PropertyDirty);
        if (GUILayout.Button("Apply"))
        {
            EditorUtility.SetDirty(targetUniFont);
            targetUniFont.ApplyPropertyChanges();
            m_PropertyDirty = false;
        }
        EditorGUI.EndDisabledGroup();

        serializedObject.ApplyModifiedProperties();
    }
}

/// <summary>
/// Custom editor for UniText_UGUI component.
/// Provides a comprehensive interface for editing text properties including:
/// - Text content with multi-line support
/// - Font settings and sizing
/// - Alignment controls (horizontal and vertical)
/// - Overflow handling options
/// - Material and color properties
/// - Spacing controls (line and character spacing)
/// - Utility functions for common operations
/// - Debug information during play mode
/// </summary>
[CustomEditor(typeof(UniText_UGUI))]
public class UniTextEditor : Editor
{
    // Serialized Properties
    private SerializedProperty m_TextInEditor;
    private SerializedProperty m_Font;
    private SerializedProperty m_FontSize;
    private SerializedProperty m_HorizontalAlignment;
    private SerializedProperty m_VerticalAlignment;
    private SerializedProperty m_HorizontalOverflow;
    private SerializedProperty m_VerticalOverflow;
    private SerializedProperty m_Color;
    private SerializedProperty m_Material;
    private SerializedProperty m_RaycastTarget;

    // Foldout states
    private bool m_TextFoldout = true;
    private bool m_FontFoldout = true;
    private bool m_AlignmentFoldout = true;
    private bool m_OverflowFoldout = true;
    private bool m_MaterialFoldout = false;

    private UniText_UGUI m_Target;

    private void OnEnable()
    {
        m_Target = target as UniText_UGUI;

        // Find serialized properties
        m_TextInEditor = serializedObject.FindProperty("m_TextInEditor");
        m_Font = serializedObject.FindProperty("m_Font");
        m_FontSize = serializedObject.FindProperty("m_FontSize");
        m_HorizontalAlignment = serializedObject.FindProperty("m_HorizontalAlignment");
        m_VerticalAlignment = serializedObject.FindProperty("m_VerticalAlignment");
        m_HorizontalOverflow = serializedObject.FindProperty("m_HorizontalOverflow");
        m_VerticalOverflow = serializedObject.FindProperty("m_VerticalOverflow");
        m_Color = serializedObject.FindProperty("m_Color");
        m_Material = serializedObject.FindProperty("m_Material");
        m_RaycastTarget = serializedObject.FindProperty("m_RaycastTarget");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        DrawTextSection();
        EditorGUILayout.Space();
        DrawFontSection();
        EditorGUILayout.Space();
        DrawAlignmentSection();
        EditorGUILayout.Space();
        DrawOverflowSection();
        EditorGUILayout.Space();
        DrawMaterialSection();
        EditorGUILayout.Space();
        DrawRaycastSection();

        // Utility buttons
        DrawUtilityButtons();

        // Show debug information in play mode
        if (Application.isPlaying)
        {
            EditorGUILayout.Space();
            DrawDebugInfo();
        }

        // Apply runtime properties if in editor mode
        if (!Application.isPlaying && GUI.changed)
        {
            ApplyEditorChanges();
        }

        serializedObject.ApplyModifiedProperties();
    }

    private void DrawTextSection()
    {
        m_TextFoldout = EditorGUILayout.Foldout(m_TextFoldout, "Text", true);
        if (m_TextFoldout)
        {
            EditorGUI.indentLevel++;

            if (m_TextInEditor != null)
            {
                // Use multi-line TextArea for proper multi-line editing
                EditorGUI.BeginChangeCheck();
                string currentText = m_TextInEditor.stringValue ?? "";
                string newText = EditorGUILayout.TextArea(currentText, GUILayout.MinHeight(60), GUILayout.MaxHeight(120));
                if (EditorGUI.EndChangeCheck())
                {
                    m_TextInEditor.stringValue = newText;
                }
            }
            else
            {
                // Fallback for runtime text display - use multi-line TextArea
                EditorGUI.BeginChangeCheck();
                string currentText = m_Target.text ?? "";
                string newText = EditorGUILayout.TextArea(currentText, GUILayout.MinHeight(60), GUILayout.MaxHeight(120));
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(m_Target, "Change Text");
                    m_Target.text = newText;
                    EditorUtility.SetDirty(m_Target);
                }
            }

            EditorGUI.indentLevel--;
        }
    }

    private void DrawFontSection()
    {
        m_FontFoldout = EditorGUILayout.Foldout(m_FontFoldout, "Font", true);
        if (m_FontFoldout)
        {
            EditorGUI.indentLevel++;

            EditorGUILayout.PropertyField(m_Font, new GUIContent("Font Asset"));
            EditorGUILayout.PropertyField(m_FontSize, new GUIContent("Font Size"));

            // Spacing controls (if available in UniTextBridge)
            if (Application.isPlaying && m_Target.uniText != null)
            {
                EditorGUI.BeginChangeCheck();
                float lineSpacing = EditorGUILayout.FloatField("Line Spacing", m_Target.uniText.lineSpacing);
                float charSpacing = EditorGUILayout.FloatField("Character Spacing", m_Target.uniText.characterSpacing);

                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(m_Target, "Change Spacing");
                    m_Target.uniText.lineSpacing = lineSpacing;
                    m_Target.uniText.characterSpacing = charSpacing;
                    EditorUtility.SetDirty(m_Target);
                }
            }
            else
            {
                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.FloatField("Line Spacing", 1.0f);
                EditorGUILayout.FloatField("Character Spacing", 0.0f);
                EditorGUI.EndDisabledGroup();
                EditorGUILayout.HelpBox("Spacing controls are available during play mode.", MessageType.Info);
            }

            EditorGUI.indentLevel--;
        }
    }

    private void DrawAlignmentSection()
    {
        m_AlignmentFoldout = EditorGUILayout.Foldout(m_AlignmentFoldout, "Alignment", true);
        if (m_AlignmentFoldout)
        {
            EditorGUI.indentLevel++;

            EditorGUILayout.PropertyField(m_HorizontalAlignment, new GUIContent("Horizontal"));
            EditorGUILayout.PropertyField(m_VerticalAlignment, new GUIContent("Vertical"));

            EditorGUI.indentLevel--;
        }
    }

    private void DrawOverflowSection()
    {
        m_OverflowFoldout = EditorGUILayout.Foldout(m_OverflowFoldout, "Overflow", true);
        if (m_OverflowFoldout)
        {
            EditorGUI.indentLevel++;

            EditorGUILayout.PropertyField(m_HorizontalOverflow, new GUIContent("Horizontal"));
            EditorGUILayout.PropertyField(m_VerticalOverflow, new GUIContent("Vertical"));

            EditorGUI.indentLevel--;
        }
    }

    private void DrawMaterialSection()
    {
        m_MaterialFoldout = EditorGUILayout.Foldout(m_MaterialFoldout, "Material", true);
        if (m_MaterialFoldout)
        {
            EditorGUI.indentLevel++;

            EditorGUILayout.PropertyField(m_Color, new GUIContent("Color"));
            EditorGUILayout.PropertyField(m_Material, new GUIContent("Material"));

            EditorGUI.indentLevel--;
        }
    }

    private void DrawRaycastSection()
    {
        EditorGUILayout.PropertyField(m_RaycastTarget, new GUIContent("Raycast Target"));
    }

    private void DrawUtilityButtons()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Utilities", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("Clear Text"))
        {
            Undo.RecordObject(m_Target, "Clear Text");
            if (m_TextInEditor != null)
                m_TextInEditor.stringValue = "";
            else
                m_Target.text = "";
            EditorUtility.SetDirty(m_Target);
        }

        if (GUILayout.Button("Sample Text"))
        {
            Undo.RecordObject(m_Target, "Set Sample Text");
            string sampleText = "The quick brown fox jumps over the lazy dog.\n这是一个测试文本。\n日本語のテストテキストです。";
            if (m_TextInEditor != null)
                m_TextInEditor.stringValue = sampleText;
            else
                m_Target.text = sampleText;
            EditorUtility.SetDirty(m_Target);
        }

        EditorGUILayout.EndHorizontal();

        if (GUILayout.Button("Force Rebuild"))
        {
            m_Target.SetAllDirty();
            EditorUtility.SetDirty(m_Target);
        }
    }

    private void ApplyEditorChanges()
    {
        if (m_Target.uniText != null)
        {
            m_Target.uniText.fontSize = m_Target.fontSize;
            m_Target.uniText.horizontalAlignment = m_Target.horizontalAlignment;
            m_Target.uniText.verticalAlignment = m_Target.verticalAlignment;
            m_Target.uniText.horizontalOverflow = m_Target.horizontalOverflow;
            m_Target.uniText.verticalOverflow = m_Target.verticalOverflow;
        }
    }

    // Utility methods for better editor experience
    private void ShowTextPreview()
    {
        if (m_Target != null && !string.IsNullOrEmpty(m_Target.text))
        {
            EditorGUILayout.LabelField("Preview:", EditorStyles.boldLabel);
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.TextArea(m_Target.text, GUILayout.MaxHeight(100));
            EditorGUI.EndDisabledGroup();
        }
    }

    private void DrawDebugInfo()
    {
        if (Application.isPlaying && m_Target.uniText != null)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Debug Info", EditorStyles.boldLabel);
            EditorGUI.indentLevel++;

            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.TextField("Current Text", m_Target.text ?? "");
            EditorGUILayout.IntField("Font Size", m_Target.uniText.fontSize);
            EditorGUILayout.EnumPopup("H Alignment", m_Target.uniText.horizontalAlignment);
            EditorGUILayout.EnumPopup("V Alignment", m_Target.uniText.verticalAlignment);
            EditorGUILayout.FloatField("Pixels Per Unit", m_Target.pixelsPerUnit);
            EditorGUI.EndDisabledGroup();

            EditorGUI.indentLevel--;
        }
    }
}
